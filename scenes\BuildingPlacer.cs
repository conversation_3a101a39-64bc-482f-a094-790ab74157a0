using Godot;
using System.Collections.Generic;

public partial class BuildingPlacer : Node2D
{
	[Export] public PackedScene AnvilScene { get; set; }
	[Export] public PackedScene BridgeScene { get; set; }
	[Export] public PackedScene CampfireScene { get; set; }
	[Export] public PackedScene Furnace1Scene { get; set; }
	[Export] public PackedScene Furnace2Scene { get; set; }
	[Export] public PackedScene Furnace3Scene { get; set; }
	[Export] public PackedScene Furnace4Scene { get; set; }
	[Export] public PackedScene GrindstoneScene { get; set; }
	[Export] public PackedScene WorkbenchScene { get; set; }
	[Export] public PackedScene SeedMakerScene { get; set; }
	[Export] public PackedScene CheesePressScene { get; set; }
	[Export] public PackedScene KegScene { get; set; }
	[Export] public PackedScene MayoMakerScene { get; set; }
	[Export] public PackedScene SmokerScene { get; set; }
	[Export] public PackedScene JamMakerScene { get; set; }
	[Export] public PackedScene OilMakerScene { get; set; }
	// Task 50 - Windmill scene
	[Export] public PackedScene WindmillScene { get; set; }
	// Task 53 - Palisade scene
	[Export] public PackedScene PalisadeScene { get; set; }

	[Export] public AudioStream BuildingPlaced { get; set; }
	
	private Node2D _currentBuilding;
	private bool _isPlacingBuilding = false;
	private Camera2D _camera;
	private EffectPlayer _effectPlayer;
	private List<TilePlaceholder> _placementTiles = new();
	private PackedScene _tilePlaceholderScene;
	
	public override void _Ready()
	{
		_effectPlayer = GetNode<EffectPlayer>("EffectPlayer");
		_camera = GetNode<Camera2D>("/root/world/Player/Camera2D");
		if (_camera == null)
		{
			GD.PrintErr("BuildingPlacer: Camera2D not found!");
		}

		_tilePlaceholderScene = GD.Load<PackedScene>("res://scenes/TilePlaceholder.tscn");
		if (_tilePlaceholderScene == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to load TilePlaceholder scene");
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlacingBuilding || _currentBuilding == null) return;

		if (@event is InputEventMouseButton mouseButton)
		{
			if (mouseButton.Pressed)
			{
				if (mouseButton.ButtonIndex == MouseButton.Left)
				{
					TryPlaceBuilding();
				}
				else if (mouseButton.ButtonIndex == MouseButton.Right)
				{
					CancelBuildingPlacement();
				}
			}
		}
		else if (@event is InputEventMouseMotion)
		{
			UpdateBuildingPosition();
		}
	}

	public void StartPlacingAnvil()
	{
		if (AnvilScene == null)
		{
			GD.PrintErr("BuildingPlacer: AnvilScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = AnvilScene.Instantiate<Anvil>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Anvil!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingBridge()
	{
		if (BridgeScene == null)
		{
			GD.PrintErr("BuildingPlacer: BridgeScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = BridgeScene.Instantiate<Bridge>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Bridge!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingCampfire()
	{
		if (CampfireScene == null)
		{
			GD.PrintErr("BuildingPlacer: CampfireScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = CampfireScene.Instantiate<Campfire>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Campfire!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingFurnace1()
	{
		if (Furnace1Scene == null)
		{
			GD.PrintErr("BuildingPlacer: Furnace1Scene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = Furnace1Scene.Instantiate<Furnace1>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Furnace1!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingFurnace2()
	{
		if (Furnace2Scene == null)
		{
			GD.PrintErr("BuildingPlacer: Furnace2Scene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = Furnace2Scene.Instantiate<Furnace2>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Furnace2!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingFurnace3()
	{
		if (Furnace3Scene == null)
		{
			GD.PrintErr("BuildingPlacer: Furnace3Scene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = Furnace3Scene.Instantiate<Furnace3>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Furnace3!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingFurnace4()
	{
		if (Furnace4Scene == null)
		{
			GD.PrintErr("BuildingPlacer: Furnace4Scene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = Furnace4Scene.Instantiate<Furnace4>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Furnace4!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingGrindstone()
	{
		if (GrindstoneScene == null)
		{
			GD.PrintErr("BuildingPlacer: GrindstoneScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = GrindstoneScene.Instantiate<Grindstone>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate grindstone!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingWorkbench()
	{
		if (WorkbenchScene == null)
		{
			GD.PrintErr("BuildingPlacer: WorkbenchScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = WorkbenchScene.Instantiate<Workbench>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate workbench!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingSeedMaker()
	{
		if (SeedMakerScene == null)
		{
			GD.PrintErr("BuildingPlacer: SeedMakerScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = SeedMakerScene.Instantiate<SeedMaker>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate seed maker!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingCheesePress()
	{
		if (CheesePressScene == null)
		{
			GD.PrintErr("BuildingPlacer: CheesePressScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = CheesePressScene.Instantiate<CheesePress>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate CheesePress!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingKeg()
	{
		if (KegScene == null)
		{
			GD.PrintErr("BuildingPlacer: KegScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = KegScene.Instantiate<Keg>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Keg!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	// Task 49 - Additional production building placement methods
	public void StartPlacingMayoMaker()
	{
		if (MayoMakerScene == null)
		{
			GD.PrintErr("BuildingPlacer: MayoMakerScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = MayoMakerScene.Instantiate<MayoMaker>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate MayoMaker!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingSmoker()
	{
		if (SmokerScene == null)
		{
			GD.PrintErr("BuildingPlacer: SmokerScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = SmokerScene.Instantiate<Smoker>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Smoker!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingJamMaker()
	{
		if (JamMakerScene == null)
		{
			GD.PrintErr("BuildingPlacer: JamMakerScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = JamMakerScene.Instantiate<JamMaker>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate JamMaker!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingOilMaker()
	{
		if (OilMakerScene == null)
		{
			GD.PrintErr("BuildingPlacer: OilMakerScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = OilMakerScene.Instantiate<OilMaker>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate OilMaker!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	// Task 50 - Windmill placement method
	public void StartPlacingWindmill()
	{
		if (WindmillScene == null)
		{
			GD.PrintErr("BuildingPlacer: WindmillScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = WindmillScene.Instantiate<Windmill>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Windmill!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	// Task 53 - Palisade placement method
	public void StartPlacingPalisade()
	{
		if (PalisadeScene == null)
		{
			GD.PrintErr("BuildingPlacer: PalisadeScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = PalisadeScene.Instantiate<Palisade>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Palisade!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	private (int width, int height) GetBuildingDimensions(Node2D building)
	{
		return building switch
		{
			Anvil => (2, 1),
			Bridge => (1, 1),
			Campfire => (1, 2),
			Furnace1 or Furnace2 or Furnace3 or Furnace4 => (2, 3),
			Grindstone => (1, 2),
			Workbench => (2, 1),
			SeedMaker or MayoMaker or JamMaker or CheesePress or Keg or Smoker => (1, 2),
			OilMaker => (1, 2),
			Windmill => (4, 2),
			Palisade => (1, 2),
			_ => (1, 1)
		};
	}

	private void ShowPlacementTiles(Vector2I topLeftTile, int width, int height, bool canPlace)
	{
		ClearPlacementTiles();

		if (_tilePlaceholderScene == null) return;

		Color tileColor = canPlace ? new Color(0.0f, 1.0f, 0.5f, 0.8f) : new Color(1.0f, 0.5f, 0.5f, 0.8f);

		for (int x = 0; x < width; x++)
		{
			for (int y = 0; y < height; y++)
			{
				Vector2I tilePos = topLeftTile + new Vector2I(x, y);

				var tilePlaceholder = _tilePlaceholderScene.Instantiate<TilePlaceholder>();
				if (tilePlaceholder != null)
				{
					// Add to world scene, not BuildingPlacer
					var worldNode = GetNode<Node>("/root/world");
					if (worldNode != null)
					{
						worldNode.AddChild(tilePlaceholder);
						tilePlaceholder.ShowAtTile(tilePos);
						tilePlaceholder.Modulate = tileColor;
						_placementTiles.Add(tilePlaceholder);
					}
				}
			}
		}
	}

	private void ClearPlacementTiles()
	{
		foreach (var tile in _placementTiles)
		{
			if (IsInstanceValid(tile))
			{
				tile.QueueFree();
			}
		}
		_placementTiles.Clear();
	}

	private void UpdateBuildingPosition()
	{
		if (_currentBuilding == null) return;

		Vector2 mousePos = GetGlobalMousePosition();

		// Safety check: Limit building placement distance from player to prevent camera teleportation
		var player = GetNode<Node2D>("/root/world/Player");
		if (player != null)
		{
			Vector2 playerPos = player.GlobalPosition;
			float maxDistance = 320.0f; // 20 tiles * 16 pixels per tile

			Vector2 direction = (mousePos - playerPos);
			if (direction.Length() > maxDistance)
			{
				mousePos = playerPos + direction.Normalized() * maxDistance;
			}
		}

		int tileX = Mathf.FloorToInt(mousePos.X / 16.0f);
		int tileY = Mathf.FloorToInt(mousePos.Y / 16.0f);
		Vector2I topLeftTile = new Vector2I(tileX, tileY);

		bool canPlace = false;

		if (_currentBuilding is Anvil anvil)
		{
			anvil.SetTilePosition(topLeftTile);
			canPlace = anvil.CanBePlacedAt(topLeftTile);
			anvil.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Bridge bridge)
		{
			bridge.SetTilePosition(topLeftTile);
			canPlace = bridge.CanBePlacedAt(topLeftTile);
			bridge.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Campfire campfire)
		{
			campfire.SetTilePosition(topLeftTile);
			canPlace = campfire.CanBePlacedAt(topLeftTile);
			campfire.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Furnace1 furnace1)
		{
			furnace1.SetTilePosition(topLeftTile);
			canPlace = furnace1.CanBePlacedAt(topLeftTile);
			furnace1.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Furnace2 furnace2)
		{
			furnace2.SetTilePosition(topLeftTile);
			canPlace = furnace2.CanBePlacedAt(topLeftTile);
			furnace2.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Furnace3 furnace3)
		{
			furnace3.SetTilePosition(topLeftTile);
			canPlace = furnace3.CanBePlacedAt(topLeftTile);
			furnace3.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Furnace4 furnace4)
		{
			furnace4.SetTilePosition(topLeftTile);
			canPlace = furnace4.CanBePlacedAt(topLeftTile);
			furnace4.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Grindstone grindstone)
		{
			grindstone.SetTilePosition(topLeftTile);
			canPlace = grindstone.CanBePlacedAt(topLeftTile);
			grindstone.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Workbench workbench)
		{
			workbench.SetTilePosition(topLeftTile);
			canPlace = workbench.CanBePlacedAt(topLeftTile);
			workbench.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is SeedMaker seedMaker)
		{
			seedMaker.SetTilePosition(topLeftTile);
			canPlace = seedMaker.CanBePlacedAt(topLeftTile);
			seedMaker.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is CheesePress cheesePress)
		{
			cheesePress.SetTilePosition(topLeftTile);
			canPlace = cheesePress.CanBePlacedAt(topLeftTile);
			cheesePress.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Keg keg)
		{
			keg.SetTilePosition(topLeftTile);
			canPlace = keg.CanBePlacedAt(topLeftTile);
			keg.SetPlacementFeedback(canPlace);
		}
		// Task 49 - Additional production buildings
		else if (_currentBuilding is MayoMaker mayoMaker)
		{
			mayoMaker.SetTilePosition(topLeftTile);
			canPlace = mayoMaker.CanBePlacedAt(topLeftTile);
			mayoMaker.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Smoker smoker)
		{
			smoker.SetTilePosition(topLeftTile);
			canPlace = smoker.CanBePlacedAt(topLeftTile);
			smoker.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is JamMaker jamMaker)
		{
			jamMaker.SetTilePosition(topLeftTile);
			canPlace = jamMaker.CanBePlacedAt(topLeftTile);
			jamMaker.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is OilMaker oilMaker)
		{
			oilMaker.SetTilePosition(topLeftTile);
			canPlace = oilMaker.CanBePlacedAt(topLeftTile);
			oilMaker.SetPlacementFeedback(canPlace);
		}
		// Task 50 - Windmill handling
		else if (_currentBuilding is Windmill windmill)
		{
			windmill.SetTilePosition(topLeftTile);
			canPlace = windmill.CanBePlacedAt(topLeftTile);
			windmill.SetPlacementFeedback(canPlace);
		}

		var (width, height) = GetBuildingDimensions(_currentBuilding);
		ShowPlacementTiles(topLeftTile, width, height, canPlace);
	}

	private void TryPlaceBuilding()
	{
		if (_currentBuilding == null) return;

		Vector2I topLeftTile = Vector2I.Zero;
		bool canPlace = false;

		if (_currentBuilding is Anvil anvil)
		{
			topLeftTile = anvil.GetTopLeftTilePosition();
			canPlace = anvil.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				anvil.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Bridge bridge)
		{
			topLeftTile = bridge.GetTopLeftTilePosition();
			canPlace = bridge.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				bridge.PlaceBuilding();
				_effectPlayer.Play(BuildingPlaced);
			}
		}
		else if (_currentBuilding is Campfire campfire)
		{
			topLeftTile = campfire.GetTopLeftTilePosition();
			canPlace = campfire.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				campfire.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Furnace1 furnace1)
		{
			topLeftTile = furnace1.GetTopLeftTilePosition();
			canPlace = furnace1.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				furnace1.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Furnace2 furnace2)
		{
			topLeftTile = furnace2.GetTopLeftTilePosition();
			canPlace = furnace2.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				furnace2.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Furnace3 furnace3)
		{
			topLeftTile = furnace3.GetTopLeftTilePosition();
			canPlace = furnace3.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				furnace3.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Furnace4 furnace4)
		{
			topLeftTile = furnace4.GetTopLeftTilePosition();
			canPlace = furnace4.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				furnace4.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Grindstone grindstone)
		{
			topLeftTile = grindstone.GetTopLeftTilePosition();
			canPlace = grindstone.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				grindstone.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Workbench workbench)
		{
			topLeftTile = workbench.GetTopLeftTilePosition();
			canPlace = workbench.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				workbench.PlaceBuilding();
			}
		}
		else if (_currentBuilding is SeedMaker seedMaker)
		{
			topLeftTile = seedMaker.GetTopLeftTilePosition();
			canPlace = seedMaker.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				seedMaker.PlaceBuilding();
			}
		}
		else if (_currentBuilding is CheesePress cheesePress)
		{
			topLeftTile = cheesePress.GetTopLeftTilePosition();
			canPlace = cheesePress.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				cheesePress.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Keg keg)
		{
			topLeftTile = keg.GetTopLeftTilePosition();
			canPlace = keg.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				keg.PlaceBuilding();
			}
		}
		// Task 49 - Additional production buildings
		else if (_currentBuilding is MayoMaker mayoMaker)
		{
			topLeftTile = mayoMaker.GetTopLeftTilePosition();
			canPlace = mayoMaker.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				mayoMaker.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Smoker smoker)
		{
			topLeftTile = smoker.GetTopLeftTilePosition();
			canPlace = smoker.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				smoker.PlaceBuilding();
			}
		}
		else if (_currentBuilding is JamMaker jamMaker)
		{
			topLeftTile = jamMaker.GetTopLeftTilePosition();
			canPlace = jamMaker.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				jamMaker.PlaceBuilding();
			}
		}
		else if (_currentBuilding is OilMaker oilMaker)
		{
			topLeftTile = oilMaker.GetTopLeftTilePosition();
			canPlace = oilMaker.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				oilMaker.PlaceBuilding();
			}
		}
		// Task 50 - Windmill handling
		else if (_currentBuilding is Windmill windmill)
		{
			topLeftTile = windmill.GetTopLeftTilePosition();
			canPlace = windmill.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				windmill.PlaceBuilding();
			}
		}
		// Task 53 - Palisade handling
		else if (_currentBuilding is Palisade palisade)
		{
			topLeftTile = palisade.GetTopLeftTilePosition();
			canPlace = palisade.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				palisade.PlaceBuilding();
			}
		}

		if (canPlace)
		{
			_currentBuilding = null;
			_isPlacingBuilding = false;
			ClearPlacementTiles();

			// Re-enable player movement when building is placed
			CommonSignals.Instance?.EmitPlayerMovementEnabled(true);
		}
	}

	private void CancelBuildingPlacement()
	{
		if (_currentBuilding != null)
		{
			_currentBuilding.QueueFree();
			_currentBuilding = null;
		}

		_isPlacingBuilding = false;
		ClearPlacementTiles();

		// Re-enable player movement when building placement is cancelled
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);
	}

	public bool IsPlacingBuilding()
	{
		return _isPlacingBuilding;
	}
}
