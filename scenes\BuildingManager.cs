using Godot;
using System.Collections.Generic;

public partial class BuildingManager : Node2D
{
	[Export] public PackedScene AnvilScene { get; set; }
	[Export] public PackedScene BridgeScene { get; set; }
	[Export] public PackedScene CampfireScene { get; set; }
	[Export] public PackedScene Furnace1Scene { get; set; }
	[Export] public PackedScene Furnace2Scene { get; set; }
	[Export] public PackedScene Furnace3Scene { get; set; }
	[Export] public PackedScene Furnace4Scene { get; set; }
	[Export] public PackedScene GrindstoneScene { get; set; }
	[Export] public PackedScene WorkbenchScene { get; set; }
	[Export] public PackedScene SeedMakerScene { get; set; }
	[Export] public PackedScene CheesePressScene { get; set; }
	[Export] public PackedScene KegScene { get; set; }
	// Task 49 - Additional production buildings
	[Export] public PackedScene MayoMakerScene { get; set; }
	[Export] public PackedScene SmokerScene { get; set; }
	[Export] public PackedScene JamMakerScene { get; set; }
	[Export] public PackedScene OilMakerScene { get; set; }
	// Task 50 - Windmill scene
	[Export] public PackedScene WindmillScene { get; set; }
	// Task 53 - Palisade scene
	[Export] public PackedScene PalisadeScene { get; set; }

	private Dictionary<string, Node2D> _activeBuildings = new();

	public override void _Ready()
	{
		if (AnvilScene == null)
		{
			AnvilScene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Anvil.tscn");
		}

		if (BridgeScene == null)
		{
			BridgeScene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Bridge.tscn");
		}

		if (CampfireScene == null)
		{
			CampfireScene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Campfire.tscn");
		}

		if (Furnace1Scene == null)
		{
			Furnace1Scene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Furnace1.tscn");
		}

		if (Furnace2Scene == null)
		{
			Furnace2Scene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Furnace2.tscn");
		}

		if (Furnace3Scene == null)
		{
			Furnace3Scene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Furnace3.tscn");
		}

		if (Furnace4Scene == null)
		{
			Furnace4Scene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Furnace4.tscn");
		}

		if (GrindstoneScene == null)
		{
			GrindstoneScene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Grindstone.tscn");
		}

		if (WorkbenchScene == null)
		{
			WorkbenchScene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Workbench.tscn");
			GD.Print($"BuildingManager: Loaded WorkbenchScene: {WorkbenchScene?.ResourcePath}");
		}

		if (SeedMakerScene == null)
		{
			SeedMakerScene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/SeedMaker.tscn");
		}

		if (PalisadeScene == null)
		{
			PalisadeScene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Palisade.tscn");
		}

		CallDeferred(nameof(LoadExistingBuildings));
	}

	private void LoadExistingBuildings()
	{
		var buildings = ResourcesManager.Instance?.GetBuildings();
		if (buildings == null || buildings.Count == 0)
		{
			return;
		}

		foreach (var buildingData in buildings)
		{
			LoadBuilding(buildingData);
		}
	}

	private bool LoadBuilding(BuildingData buildingData)
	{
		if (string.IsNullOrEmpty(buildingData.BuildingType))
		{
			return false;
		}

		if (_activeBuildings.ContainsKey(buildingData.Id))
		{
			return false;
		}

		Node2D buildingInstance = null;

		switch (buildingData.BuildingType)
		{
			case "Anvil":
				buildingInstance = LoadAnvil(buildingData);
				break;
			case "Bridge":
				buildingInstance = LoadBridge(buildingData);
				break;
			case "Campfire":
				buildingInstance = LoadCampfire(buildingData);
				break;
			case "Furnace1":
				buildingInstance = LoadFurnace1(buildingData);
				break;
			case "Furnace2":
				buildingInstance = LoadFurnace2(buildingData);
				break;
			case "Furnace3":
				buildingInstance = LoadFurnace3(buildingData);
				break;
			case "Furnace4":
				buildingInstance = LoadFurnace4(buildingData);
				break;
			case "Grindstone":
				buildingInstance = LoadGrindstone(buildingData);
				break;
			case "Workbench":
				buildingInstance = LoadWorkbench(buildingData);
				break;
			case "SeedMaker":
				buildingInstance = LoadSeedMaker(buildingData);
				break;
			case "CheesePress":
				buildingInstance = LoadCheesePress(buildingData);
				break;
			case "Keg":
				buildingInstance = LoadKeg(buildingData);
				break;
			// Task 49 - Additional production buildings
			case "MayoMaker":
				buildingInstance = LoadMayoMaker(buildingData);
				break;
			case "Smoker":
				buildingInstance = LoadSmoker(buildingData);
				break;
			case "JamMaker":
				buildingInstance = LoadJamMaker(buildingData);
				break;
			case "OilMaker":
				buildingInstance = LoadOilMaker(buildingData);
				break;
			// Task 50 - Windmill
			case "Windmill":
				buildingInstance = LoadWindmill(buildingData);
				break;
			// Task 53 - Palisade
			case "Palisade":
				buildingInstance = LoadPalisade(buildingData);
				break;
			default:
				return false;
		}

		if (buildingInstance != null)
		{
			_activeBuildings[buildingData.Id] = buildingInstance;
			return true;
		}

		return false;
	}

	private Anvil LoadAnvil(BuildingData buildingData)
	{
		if (AnvilScene == null)
		{
			return null;
		}

		var anvil = AnvilScene.Instantiate<Anvil>();
		if (anvil == null)
		{
			return null;
		}

		anvil.LoadFromSaveData(buildingData);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", anvil);
		}
		else
		{
			GetParent().CallDeferred("add_child", anvil);
		}

		return anvil;
	}

	private Bridge LoadBridge(BuildingData buildingData)
	{
		if (BridgeScene == null)
		{
			return null;
		}

		var bridge = BridgeScene.Instantiate<Bridge>();
		if (bridge == null)
		{
			return null;
		}

		// Get the required references and pass them to the bridge
		var customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		var bridgeTileMapLayer = GetNode<TileMapLayer>("/root/world/Layer2Floor_Bridge_SpeedModifier");

		bridge.LoadFromSaveData(buildingData, customDataManager, bridgeTileMapLayer);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", bridge);
		}
		else
		{
			GetParent().CallDeferred("add_child", bridge);
		}

		return bridge;
	}

	private Campfire LoadCampfire(BuildingData buildingData)
	{
		if (CampfireScene == null)
		{
			return null;
		}

		var campfire = CampfireScene.Instantiate<Campfire>();
		if (campfire == null)
		{
			return null;
		}

		campfire.LoadFromSaveData(buildingData);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", campfire);
		}
		else
		{
			GetParent().CallDeferred("add_child", campfire);
		}

		return campfire;
	}

	private Furnace1 LoadFurnace1(BuildingData buildingData)
	{
		if (Furnace1Scene == null)
		{
			return null;
		}

		var furnace = Furnace1Scene.Instantiate<Furnace1>();
		if (furnace == null)
		{
			return null;
		}

		furnace.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
							GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
							buildingData.CurrentHealth);
		furnace.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", furnace);
		}
		else
		{
			GetParent().CallDeferred("add_child", furnace);
		}

		return furnace;
	}

	private Furnace2 LoadFurnace2(BuildingData buildingData)
	{
		if (Furnace2Scene == null)
		{
			return null;
		}

		var furnace = Furnace2Scene.Instantiate<Furnace2>();
		if (furnace == null)
		{
			return null;
		}

		furnace.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
							GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
							buildingData.CurrentHealth);
		furnace.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", furnace);
		}
		else
		{
			GetParent().CallDeferred("add_child", furnace);
		}

		return furnace;
	}

	private Furnace3 LoadFurnace3(BuildingData buildingData)
	{
		if (Furnace3Scene == null)
		{
			return null;
		}

		var furnace = Furnace3Scene.Instantiate<Furnace3>();
		if (furnace == null)
		{
			return null;
		}

		furnace.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
							GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
							buildingData.CurrentHealth);
		furnace.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", furnace);
		}
		else
		{
			GetParent().CallDeferred("add_child", furnace);
		}

		return furnace;
	}

	private Furnace4 LoadFurnace4(BuildingData buildingData)
	{
		if (Furnace4Scene == null)
		{
			return null;
		}

		var furnace = Furnace4Scene.Instantiate<Furnace4>();
		if (furnace == null)
		{
			return null;
		}

		furnace.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
							GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
							buildingData.CurrentHealth);
		furnace.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", furnace);
		}
		else
		{
			GetParent().CallDeferred("add_child", furnace);
		}

		return furnace;
	}

	private Grindstone LoadGrindstone(BuildingData buildingData)
	{
		if (GrindstoneScene == null)
		{
			return null;
		}

		var grindstone = GrindstoneScene.Instantiate<Grindstone>();
		if (grindstone == null)
		{
			return null;
		}

		grindstone.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
							GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
							buildingData.CurrentHealth);
		grindstone.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", grindstone);
		}
		else
		{
			GetParent().CallDeferred("add_child", grindstone);
		}

		return grindstone;
	}

	private Workbench LoadWorkbench(BuildingData buildingData)
	{
		GD.Print($"BuildingManager: Loading Workbench from save data - ID: {buildingData.Id}, BuildingType: {buildingData.BuildingType}");

		if (WorkbenchScene == null)
		{
			GD.PrintErr("BuildingManager: WorkbenchScene is null!");
			return null;
		}

		GD.Print($"BuildingManager: Instantiating Workbench from scene: {WorkbenchScene.ResourcePath}");
		var workbench = WorkbenchScene.Instantiate<Workbench>();
		if (workbench == null)
		{
			return null;
		}

		// Set the CustomDataLayerManager before loading from save data
		var customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		workbench.SetCustomDataManager(customDataManager);

		workbench.LoadFromSaveData(buildingData);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", workbench);
		}
		else
		{
			GetParent().CallDeferred("add_child", workbench);
		}

		return workbench;
	}

	private SeedMaker LoadSeedMaker(BuildingData buildingData)
	{
		if (SeedMakerScene == null)
		{
			return null;
		}

		var seedMaker = SeedMakerScene.Instantiate<SeedMaker>();
		if (seedMaker == null)
		{
			return null;
		}

		seedMaker.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
							GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
							buildingData.CurrentHealth);
		seedMaker.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", seedMaker);
		}
		else
		{
			GetParent().CallDeferred("add_child", seedMaker);
		}

		return seedMaker;
	}

	private CheesePress LoadCheesePress(BuildingData buildingData)
	{
		if (CheesePressScene == null)
		{
			return null;
		}

		var cheesePress = CheesePressScene.Instantiate<CheesePress>();
		if (cheesePress == null)
		{
			return null;
		}

		cheesePress.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
							GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
							buildingData.CurrentHealth);
		cheesePress.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", cheesePress);
		}
		else
		{
			GetParent().CallDeferred("add_child", cheesePress);
		}

		return cheesePress;
	}

	private Keg LoadKeg(BuildingData buildingData)
	{
		if (KegScene == null)
		{
			return null;
		}

		var keg = KegScene.Instantiate<Keg>();
		if (keg == null)
		{
			return null;
		}

		keg.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
						GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
						buildingData.CurrentHealth);
		keg.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", keg);
		}
		else
		{
			GetParent().CallDeferred("add_child", keg);
		}

		return keg;
	}

	// Task 49 - Additional production building load methods
	private MayoMaker LoadMayoMaker(BuildingData buildingData)
	{
		if (MayoMakerScene == null)
		{
			return null;
		}

		var mayoMaker = MayoMakerScene.Instantiate<MayoMaker>();
		if (mayoMaker == null)
		{
			return null;
		}

		mayoMaker.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
						GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
						buildingData.CurrentHealth);
		mayoMaker.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", mayoMaker);
		}
		else
		{
			GetParent().CallDeferred("add_child", mayoMaker);
		}

		return mayoMaker;
	}

	private Smoker LoadSmoker(BuildingData buildingData)
	{
		if (SmokerScene == null)
		{
			return null;
		}

		var smoker = SmokerScene.Instantiate<Smoker>();
		if (smoker == null)
		{
			return null;
		}

		smoker.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
						GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
						buildingData.CurrentHealth);
		smoker.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", smoker);
		}
		else
		{
			GetParent().CallDeferred("add_child", smoker);
		}

		return smoker;
	}

	private JamMaker LoadJamMaker(BuildingData buildingData)
	{
		if (JamMakerScene == null)
		{
			return null;
		}

		var jamMaker = JamMakerScene.Instantiate<JamMaker>();
		if (jamMaker == null)
		{
			return null;
		}

		jamMaker.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
						GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
						buildingData.CurrentHealth);
		jamMaker.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", jamMaker);
		}
		else
		{
			GetParent().CallDeferred("add_child", jamMaker);
		}

		return jamMaker;
	}

	private OilMaker LoadOilMaker(BuildingData buildingData)
	{
		if (OilMakerScene == null)
		{
			return null;
		}

		var oilMaker = OilMakerScene.Instantiate<OilMaker>();
		if (oilMaker == null)
		{
			return null;
		}

		oilMaker.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
						GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
						buildingData.CurrentHealth);
		oilMaker.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", oilMaker);
		}
		else
		{
			GetParent().CallDeferred("add_child", oilMaker);
		}

		return oilMaker;
	}

	// Task 50 - Windmill load method
	private Windmill LoadWindmill(BuildingData buildingData)
	{
		if (WindmillScene == null)
		{
			return null;
		}

		var windmill = WindmillScene.Instantiate<Windmill>();
		if (windmill == null)
		{
			return null;
		}

		windmill.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
						GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
						buildingData.CurrentHealth);
		windmill.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", windmill);
		}
		else
		{
			GetParent().CallDeferred("add_child", windmill);
		}

		return windmill;
	}

	private Palisade LoadPalisade(BuildingData buildingData)
	{
		GD.Print($"BuildingManager: Loading Palisade from save data - ID: {buildingData.Id}, BuildingType: {buildingData.BuildingType}");

		if (PalisadeScene == null)
		{
			GD.PrintErr("BuildingManager: PalisadeScene is null!");
			return null;
		}

		GD.Print($"BuildingManager: Instantiating Palisade from scene: {PalisadeScene.ResourcePath}");
		var palisade = PalisadeScene.Instantiate<Palisade>();
		if (palisade == null)
		{
			return null;
		}

		palisade.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
						GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
						buildingData.CurrentHealth);
		palisade.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", palisade);
		}
		else
		{
			GetParent().CallDeferred("add_child", palisade);
		}

		return palisade;
	}

	public bool RemoveBuilding(string buildingId)
	{
		if (_activeBuildings.TryGetValue(buildingId, out var building))
		{
			building.QueueFree();
			_activeBuildings.Remove(buildingId);
			return true;
		}
		return false;
	}

	public Dictionary<string, Node2D> GetAllBuildings()
	{
		return new Dictionary<string, Node2D>(_activeBuildings);
	}

	public void ClearAllBuildings()
	{
		foreach (var building in _activeBuildings.Values)
		{
			building.QueueFree();
		}
		_activeBuildings.Clear();

		ResourcesManager.Instance?.ClearBuildings();
	}
}
